from datetime import datetime
<<<<<<< HEAD
from decimal import Decimal
=======
from enum import Enum
>>>>>>> 4ae150978150916044fa8f6f6671a6da944e2191
from typing import List, Optional

from pydantic import field_validator
from sqlalchemy import Column, DateTime, func, text
from sqlmodel import Field, select

from bisheng.cache.redis import redis_client
from bisheng.database.base import session_getter
from bisheng.database.constants import AdminRole, DefaultRole
from bisheng.database.models.base import SQLModelSerializable
from bisheng.database.models.user_group import UserGroup
from bisheng.database.models.user_role import UserRole
import string
import secrets
def generate_username(length: int = 16) -> str:
    """生成指定长度的随机字符串（字母+数字）"""
    characters = string.ascii_letters + string.digits
    return ''.join(secrets.choice(characters) for _ in range(length))

class UserBase(SQLModelSerializable):
    user_name: str = Field(index=True, unique=True,default_factory=generate_username)
    email: Optional[str] = Field(default=None, index=True)
    phone_number: Optional[str] = Field(default=None, index=True)
    dept_id: Optional[str] = Field(default=None, index=True)
    remark: Optional[str] = Field(default=None, index=False)
    delete: int = Field(default=0, index=False)
    create_time: Optional[datetime] = Field(default=None, sa_column=Column(
        DateTime, nullable=False, index=True, server_default=text('CURRENT_TIMESTAMP')))
    update_time: Optional[datetime] = Field(default=None, sa_column=Column(
        DateTime, nullable=False, server_default=text('CURRENT_TIMESTAMP'), onupdate=text('CURRENT_TIMESTAMP')))
    user_version: Optional[str] = Field(default=None, max_length=20, description="用户版本(基础版/标准版/专业版/企业版)")

    @field_validator('user_name')
    @classmethod
    def validate_str(cls, v):
        # dict_keys(['description', 'name', 'id', 'data'])
        if not v:
            raise ValueError('user_name 不能为空')
        return v


class User(UserBase, table=True):
    user_id: Optional[int] = Field(default=None, primary_key=True)
    password: str = Field(index=False,default=None)
    password_update_time: Optional[datetime] = Field(default=None, sa_column=Column(
        DateTime, nullable=False, server_default=text('CURRENT_TIMESTAMP')), description='密码最近的修改时间')


class UserRead(UserBase):
    user_id: Optional[int] = None
    role: Optional[str] = None
    access_token: Optional[str] = None
    web_menu: Optional[List[str]] = None
    admin_groups: Optional[List[int]] = None  # 所管理的用户组ID列表
    # 积分相关字段
    total_credits: Optional[Decimal] = Field(default=None, description="累计总积分")
    available_credits: Optional[Decimal] = Field(default=None, description="当前可用积分")


class UserQuery(UserBase):
    user_id: Optional[int] = None
    user_name: Optional[str] = None


class UserLogin(UserBase):
    password: Optional[str] = None
    user_name: Optional[str] = None
    user_email: Optional[str] = None
    phone: Optional[str] = None
    phone_code: Optional[str] = None
    captcha_key: Optional[str] = None
    captcha: Optional[str] = None


class UserCreate(UserBase):
    password: Optional[str] = Field(default='')
    phone: str
    phone_code: str
    captcha_key: Optional[str] = None
    captcha: Optional[str] = None

class VerificationType(str, Enum):
    LOGIN = "login"
    REGISTER = "register"
    RESET_PASSWORD = "reset_password"

class UserVerification(SQLModelSerializable):
    phone_number: str = Field(
        ...,
        max_length=20,
        description="用户的手机号码，最长20位"
    )
    verification_type: VerificationType

    @field_validator("phone_number")
    def validate_phone(cls, v):
        """
        校验手机号是否符合基本格式（仅包含数字和可接受的符号）
        """
        import re
        if not re.match(r'^[+\d][\d\- ]+$', v):
            raise ValueError("手机号格式不正确")
        return v


class UserUpdate(SQLModelSerializable):
    user_id: int
    delete: Optional[int] = 0

class UserUpdateByPhone(SQLModelSerializable):
    user_id: int
    user_name: str
    password: str

class UserDao(UserBase):

    @classmethod
    def get_user(cls, user_id: int) -> User | None:
        with session_getter() as session:
            statement = select(User).where(User.user_id == user_id)
            return session.exec(statement).first()

    @classmethod
    def get_user_by_ids(cls, user_ids: List[int]) -> List[User] | None:
        with session_getter() as session:
            statement = select(User).where(User.user_id.in_(user_ids))
            return session.exec(statement).all()

    @classmethod
    def get_user_by_username(cls, username: str) -> User | None:
        with session_getter() as session:
            statement = select(User).where(User.user_name == username)
            return session.exec(statement).first()

    @classmethod
    def get_user_by_phone(cls, phone: str) -> User | None:
        with session_getter() as session:
            statement = select(User).where(User.phone_number == phone)
            return session.exec(statement).first()

    @classmethod
    def get_verification_key_prefix(cls,verification_type='login') -> str :
        """
        验证码类型key
        """
        return f"{verification_type}_verification_code:"

    @classmethod
    def check_user_phone_code(cls, verification_type: str, phone_number:str, phone_code:str) -> bool | None:
        """
        验证码检验
        """
        key_prefix = UserDao.get_verification_key_prefix(verification_type)
        key = key_prefix + str(phone_number)
        stored_code = redis_client.get(key)
        if isinstance(stored_code, bytes):
            stored_code = stored_code.decode('utf-8')
        if not stored_code:
            return False
        if str(phone_code) == stored_code:
            # 验证成功后清理Redis
            redis_client.delete(key)
            return True
        else:
            return False


    @classmethod
    def update_user(cls, user: User) -> User:
        with session_getter() as session:
            session.add(user)
            session.commit()
            session.refresh(user)
            return user

    @classmethod
    def filter_users(cls,
                     user_ids: List[int],
                     keyword: str = None,
                     page: int = 0,
                     limit: int = 0) -> (List[User], int):
        statement = select(User)
        count_statement = select(func.count(User.user_id))
        if user_ids:
            statement = statement.where(User.user_id.in_(user_ids))
            count_statement = count_statement.where(User.user_id.in_(user_ids))
        if keyword:
            statement = statement.where(User.user_name.like(f'%{keyword}%'))
            count_statement = count_statement.where(User.user_name.like(f'%{keyword}%'))
        if page and limit:
            statement = statement.offset((page - 1) * limit).limit(limit)
        statement = statement.order_by(User.user_id.desc())
        with session_getter() as session:
            return session.exec(statement).all(), session.scalar(count_statement)

    @classmethod
    def get_unique_user_by_name(cls, user_name: str) -> User | None:
        with session_getter() as session:
            statement = select(User).where(User.user_name == user_name)
            return session.exec(statement).first()

    @classmethod
    def search_user_by_name(cls, user_name: str) -> List[User] | None:
        with session_getter() as session:
            statement = select(User).where(User.user_name.like('%{}%'.format(user_name)))
            return session.exec(statement).all()

    @classmethod
    def create_user(cls, db_user: User) -> User:
        with session_getter() as session:
            session.add(db_user)
            session.commit()
            session.refresh(db_user)
            return db_user

    @classmethod
    def add_user_and_default_role(cls, user: User) -> User:
        """
        新增用户，并添加默认角色
        """
        with session_getter() as session:
            session.add(user)
            session.commit()
            session.refresh(user)
            db_user_role = UserRole(user_id=user.user_id, role_id=DefaultRole)
            session.add(db_user_role)
            session.commit()
            session.refresh(user)
            return user

    @classmethod
    def add_user_and_admin_role(cls, user: User) -> User:
        """
        新增用户，并添加超级管理员角色
        """
        with session_getter() as session:
            session.add(user)
            session.commit()
            session.refresh(user)
            db_user_role = UserRole(user_id=user.user_id, role_id=AdminRole)
            session.add(db_user_role)
            session.commit()
            session.refresh(user)
            return user

    @classmethod
    def add_user_with_groups_and_roles(cls, user: User, group_ids: List[int],
                                       role_ids: List[int]) -> User:
        with session_getter() as session:
            session.add(user)
            session.flush()
            for group_id in group_ids:
                db_user_group = UserGroup(user_id=user.user_id, group_id=group_id)
                session.add(db_user_group)
            for role_id in role_ids:
                db_user_role = UserRole(user_id=user.user_id, role_id=role_id)
                session.add(db_user_role)
            session.commit()
            session.refresh(user)
            return user

    @classmethod
    def get_all_users(cls, page: int = 0, limit: int = 0) -> List[User]:
        """
        分页获取所有用户
        """
        statement = select(User)
        if page and limit:
            statement = statement.offset((page - 1) * limit).limit(limit)
        with session_getter() as session:
            return session.exec(statement).all()
