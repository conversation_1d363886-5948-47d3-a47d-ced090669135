[tool.poetry]
name = "bisheng"
version = "1.3.0"
description = "A Python package with a built-in web application"
authors = ["Dataelement <<EMAIL>>"]
maintainers = [
    "hanfeng <<EMAIL>>",
]
repository = "https://github.com/dataelement/bisheng"
license = "Apache 2.0"
readme = "README.md"
keywords = ["nlp", "langchain", "openai", "gpt", "gui"]
packages = [{ include = "bisheng", from = "./" }]
include = ["./bisheng/*", "bisheng/**/*"]


[tool.poetry.scripts]
bisheng = "bisheng.__main__:main"

[tool.poetry.dependencies]
python = ">=3.10,<3.11"
bisheng_langchain = "1.3.0"
bisheng_pyautogen = "0.3.2"
langchain = "^0.3.23"
langchain-community = "^0.3.21"
langchain_experimental = "*"
langchain-ollama = "^0.3.0"
langchain-google-genai = "^2.1.0"
langchain-anthropic = "^0.3.10"
langchain-deepseek = "^0.1.3"
langgraph = "^0.3.27"
openai = "^1.68.2"
minio = "7.2.0"
loguru = "^0.7.1"
captcha = "^0.5.0"
rsa = "4.9"
pydantic = "^2.7.2"
numexpr = "^2.8.6"
pypinyin = "0.50.0"
celery = { extras = ["redis"], version = "^5.3.6" }
redis = "^5.0.0"
jieba = "^0.42.1"
PyMuPDF = "^1.23.8"
shapely = "^2.0.1"
fastapi = "^0.115.0"
uvicorn = "^0.23.1"
beautifulsoup4 = "^4.12.2"
google-search-results = "^2.4.1"
google-api-python-client = "^2.79.0"
typer = "^0.9.0"
gunicorn = "^20.1.0"
pandas = "^2.0.0"
chromadb = "^0.3.21"
rich = "^13.4.2"
networkx = "^3.1"
unstructured = "^0.17.2"
pypdf = "*" # 和llama-index包冲突
lxml = "^4.9.2"
pysrt = "^1.1.2"
fake-useragent = "^1.1.3"
docstring-parser = "^0.15"
psycopg2-binary = "^2.9.6"
pyarrow = "^12.0.0"
tiktoken = "*"
wikipedia = "^1.4.0"
qdrant-client = "^1.3.0"
websockets = "^10.3"
weaviate-client = "^3.21.0"
cohere = "^4.11.0"
python-multipart = "^0.0.6"
sqlmodel = "^0.0.14"
pymysql = "^0.10.1"
pymilvus = "2.5.10"
elasticsearch = "^8.9.0"
orjson = "^3.9.1"
multiprocess = "^0.70.14"
cachetools = "^5.3.1"
types-cachetools = "^*******"
appdirs = "^1.4.4"
supabase = "^2.4.0"
certifi = "^2023.5.7"
psycopg = "^3.1.9"
psycopg-binary = "^3.1.9"
emoji = "^2.10.1"
platformdirs = ">=2.5"
scipy = "*"
arxiv = "2.1.0"
matplotlib = "3.8.4"
cchardet = "^2.1.7"
llama-index = "0.9.48"
tenacity = "<8.4.0"
bisheng-ragas = "^1.0.2"
qianfan = "^0.4.4"
dashscope = "^1.20.3"
blobfile = "^3.0.0"
mcp = "^1.6.0"
numpy = "^1.26.2"
pyjwt = "^1.7.1"
tencentcloud-sdk-python = "^3.0.1373"
fastapi-jwt-auth = "^0.5.0"
langchain-openai = "^0.3.16"
markdownify = "^1.1.0"
readability = "^0.3.2"
readability-lxml = "^*******"
xlrd = "^2.0.1"
openpyxl = "^3.1.5"
pypandoc = "^1.15"
python-pptx = "^1.0.2"
python-docx = "^1.1.2"
markdown = "^3.8"
tabulate = "^0.9.0"

[tool.poetry.dev-dependencies]
black = "^23.1.0"
ipykernel = "^6.21.2"
mypy = "^1.1.1"
ruff = "^0.0.254"
httpx = "^0.27.0"
pytest = "^7.2.2"
types-requests = "^2.28.11"
requests = "^2.28.0"
pytest-cov = "^4.0.0"
pandas-stubs = "^2.0.0.230412"
types-pillow = "^*******"
types-appdirs = "^*******"
types-pyyaml = "^********"

[tool.poetry.extras]
deploy = ["langchain-serve"]

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra"
testpaths = ["tests", "integration"]
console_output_style = "progress"
filterwarnings = ["ignore::DeprecationWarning"]
log_cli = true


[tool.ruff]
line-length = 120

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pyright]
reportGeneralTypeIssues = false
# 增加国内源相关
[[tool.poetry.source]]
name = "ali"
url = "https://mirrors.aliyun.com/pypi/simple/"
priority = "explicit"


[[tool.poetry.source]]
name = "tsinghua"
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple/"
priority = "primary"
